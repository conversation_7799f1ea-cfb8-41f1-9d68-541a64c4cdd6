package com.fxiaoke.stone.commons.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.domain.R;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import java.util.Map;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class CallServiceUtil {

  public static String post(OkHttpSupport client,String serverUrl, RequestBody body) {
    Request request = new Request.Builder().url(serverUrl).post(body).build();
    try {
      return (String) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response.body() == null) {
            throw new StoneCommonClientException("Call service Response body is null", 500,
                serverUrl, body);
          }
          return response.body().string();
        }
      });
    } catch (StoneCommonClientException e) {
      throw e;
    } catch (Exception e) {
      throw new StoneCommonClientException(e, "Call service fail", 500, serverUrl);
    }
  }

  public static String get(OkHttpSupport client,String serverUrl) {
    Request request = new Request.Builder().url(serverUrl).get().build();
    try {
      return (String) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response.body() == null) {
            throw new StoneCommonClientException("Call service Response body is null", 500,
                serverUrl);
          }
          return response.body().string();
        }
      });
    } catch (StoneCommonClientException e) {
      throw e;
    } catch (Exception e) {
      throw new StoneCommonClientException(e, "Call service fail", 500, serverUrl);
    }
  }

  public static <T> R<T> formJson(String json, Class<T> clazz) {
    R<T> result;
    try {
      result = JSON.parseObject(json, new TypeReference<R<T>>(clazz) {});
    } catch (Exception e) {
      throw new StoneCommonClientException(e, "Call stone-auth service,return type mismatch", 500, json);
    }
    if (!result.isSuccess()) {
      throw new StoneCommonClientException(result.getMessage(), result.getCode(), json);
    }
    return result;
  }

  /**
   * 支持自定义 Header 的 POST 请求
   *
   * <p>此方法专门用于文件上传场景，支持自定义请求头。
   * 与 {@link CallServiceUtil#post} 的区别在于支持Header定制。</p>
   *
   * @param client    OkHttp 客户端
   * @param serverUrl 请求地址
   * @param headers   自定义请求头 Map
   * @param body      请求体
   * @return 响应体字符串
   * @throws StoneCommonClientException 调用失败时抛出异常
   */
  public static String post(OkHttpSupport client, String serverUrl, Map<String, String> headers,
      RequestBody body) {

    Request.Builder requestBuilder = new Request.Builder().url(serverUrl);

    if (headers != null && !headers.isEmpty()) {
      for (Map.Entry<String, String> entry : headers.entrySet()) {
        if (entry.getKey() != null && entry.getValue() != null) {
          requestBuilder.header(entry.getKey(), entry.getValue());
        }
      }
    }

    Request request = requestBuilder.post(body).build();
    SyncCallback syncCallback = createResponseCallback(serverUrl, headers);
    return (String) client.syncExecute(request, syncCallback);
  }

  /**
   * 创建响应回调处理器
   */
  private static SyncCallback createResponseCallback(String serverUrl, Object... args) {
    return new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        if (response.body() == null) {
          throw new StoneCommonClientException("Response body is null", 500, serverUrl, args);
        }
        if (!response.isSuccessful()) {
          throw new StoneCommonClientException(response.message(), response.code(), serverUrl,
              args);
        }
        return response.body().string();
      }
    };
  }

  public static <T> T formJson(String json, String serverUrl, Class<T> clazz) {
    try {
      return JSON.parseObject(json, clazz);
    } catch (Exception e) {
      throw new StoneCommonClientException(e, "Call" + serverUrl + ",return type mismatch", 500,
          json);
    }
  }
}
